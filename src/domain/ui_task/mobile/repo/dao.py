#!/usr/bin/env python3
"""
UI任务数据库模型
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import Column, String, Text, DateTime, JSON, ForeignKey
from sqlalchemy.dialects.mysql import LONGTEXT, MEDIUMTEXT
from sqlalchemy.orm import relationship

from src.infra.clients.mysql.orm import IdModelMixin, DateTimeModelMixin
from src.schema.action_types import ExecutionStatus, ActionStatus


class UITask(IdModelMixin, DateTimeModelMixin):
    """
    UI任务表 - 存储任务基本信息和执行状态
    """
    __tablename__ = "ui_task"

    # 基本信息
    task_id = Column(String(64), unique=True, nullable=False, index=True, comment="任务唯一标识")
    task_name = Column(String(255), nullable=False, comment="任务名称")

    # 执行模式和步骤信息
    execution_mode = Column(String(32), nullable=False, default="aggregation", comment="执行模式: step_by_step/aggregation")
    task_step_by_step = Column(JSON, comment="详细步骤列表(JSON格式) - 分步模式使用")
    task_aggregation_step = Column(Text, comment="聚合步骤描述 - 聚合模式使用")

    # 应用信息
    app_id = Column(String(64), comment="应用ID")

    # 代理信息
    agent_type = Column(String(32), comment="代理类型: android/ios")
    agent_config_id = Column(String(64), comment="代理配置ID")

    # 兼容性字段 - 保留原有字段以确保向后兼容
    device_id = Column(String(64), comment="设备ID(兼容字段)")
    device_type = Column(String(32), default="android", comment="设备类型: android/ios(兼容字段)")

    # 执行状态
    status = Column(String(32), default=ExecutionStatus.PROCESSING.value, comment="任务状态: processing/succeed/failed/terminate/error")
    # 最后一步执行后的截图
    image_path = Column(String(512), comment="执行后截图地址")
    # 时间信息
    start_time = Column(DateTime, comment="开始执行时间")
    end_time = Column(DateTime, comment="结束时间")
    # 结果信息
    error_message = Column(Text, comment="错误信息")
    # 期望结果 - 支持两种模式的期望结果结构
    task_expect_result = Column(JSON, comment="任务期望结果(JSON格式,包含text和image字段)")
    # 执行日志 - 存储简洁的执行过程，执行过程中动态更新
    execution_log = Column(LONGTEXT, comment="执行日志(文本格式,记录每轮决策和动作)")
    # 关联关系
    actions = relationship("UITaskAction", back_populates="task", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<UITask(task_id='{self.task_id}', name='{self.task_name}', status='{self.status}')>"

    def to_dict(self):
        """转换为字典格式"""
        result = super().to_dict()
        # 状态已经是字符串，直接返回
        return result

    def start_execution(self):
        """开始执行任务"""
        self.status = ExecutionStatus.PROCESSING.value
        self.start_time = datetime.now()
        self.updated_at = datetime.now()

    def complete_execution(self, success: bool = True, error_message: str = None):
        """完成任务执行"""
        self.status = ExecutionStatus.SUCCEED.value if success else ExecutionStatus.FAILED.value
        self.end_time = datetime.now()
        self.updated_at = datetime.now()

        if error_message:
            self.error_message = error_message

    def update_status(self, status, error_message: Optional[str] = None):
        """更新任务状态"""
        # 如果传入的是枚举，转换为字符串值
        if hasattr(status, 'value'):
            self.status = status.value
        else:
            self.status = str(status)
        self.updated_at = datetime.now()
        if error_message:
            self.error_message = error_message

        # 设置结束时间
        status_value = self.status
        if status_value in [ExecutionStatus.SUCCEED.value, ExecutionStatus.FAILED.value, ExecutionStatus.TERMINATE.value, ExecutionStatus.ERROR.value]:
            self.end_time = datetime.now()


class UITaskAction(IdModelMixin, DateTimeModelMixin):
    """
    UI任务动作表 - 存储每个执行步骤的详细信息
    """
    __tablename__ = "ui_task_action"

    # 关联信息 - 使用外键关联主表的task_id字段
    task_id = Column(String(64), ForeignKey('ui_task.task_id'), nullable=False, index=True, comment="关联的任务UUID")
    # 执行信息
    step_name = Column(String(255), nullable=False, comment="执行的步骤名称")
    start_time = Column(DateTime, comment="开始时间")
    end_time = Column(DateTime, comment="结束时间")
    # 执行状态
    status = Column(Text, default=ActionStatus.RUNNING.value, nullable=False, comment="执行状态: running/completed/failed/terminate")
    error_message = Column(Text, comment="错误信息")

    # 决策信息
    decision_content = Column(MEDIUMTEXT, comment="决策Agent的分析内容")

    # 动作命令（完整的动作命令，包含坐标）
    action = Column(String(512), comment="动作命令(完整的动作命令，包含坐标)")

    # 截图信息 - 存储执行前截图
    image_path = Column(String(512), comment="执行前截图地址")

    # 期望结果 - 支持分步模式的步骤期望结果
    expect_result = Column(JSON, comment="步骤期望结果(JSON格式,包含text和image字段)")

    # 验证信息，如果需要验证每个步骤是否成功，这个字段填写，不验证为空
    verification_result = Column(JSON, comment="步骤验证结果")

    # 关联关系
    task = relationship("UITask", back_populates="actions")

    def __repr__(self):
        return f"<UITaskAction(task_id='{self.task_id}', step='{self.step_name}', action='{self.action}')>"

    def to_dict(self):
        """转换为字典格式"""
        result = super().to_dict()
        return result

    def start_execution(self):
        """开始执行动作"""
        self.status = ActionStatus.RUNNING.value
        self.start_time = datetime.now()
        self.updated_at = datetime.now()

    def complete_execution(self, success: bool = True, error_message: str = None, terminate: bool = False):
        """完成动作执行"""
        if terminate:
            self.status = ActionStatus.TERMINATE.value
        else:
            self.status = ActionStatus.COMPLETED.value if success else ActionStatus.FAILED.value
        self.end_time = datetime.now()
        self.updated_at = datetime.now()
        if error_message:
            self.error_message = error_message
